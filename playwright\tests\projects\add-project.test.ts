import { test, expect } from "@playwright/test";
import { PomManager } from "@@/playwright/page-object-models/pom-manager";

test.describe("Adding a project tests", () => {
  // Run in serial mode to prevent database conflicts when creating projects
  test.describe.configure({ mode: "serial" });
  let pom: PomManager;

  test.beforeEach(async ({ page }) => {
    pom = new PomManager(page);
    await pom.projectsPage.goToProjectsPage();
    await pom.projectsPage.openProjectForm();
  });

  test("should be able to open a project form with empty fields or default values", async () => {
    await expect(pom.projectForm.projectLinkInput).toHaveValue("");
    await expect(pom.projectForm.projectJobReferenceInput).toHaveValue("");
    await expect(pom.projectForm.statusInput).toHaveValue("active");
    await expect(pom.projectForm.projectTitleInput).toHaveValue("");
    const today = new Date().toISOString().split("T")[0];
    await expect(pom.projectForm.dateOfApplicationInput).toHaveValue(today);
    await expect(pom.projectForm.dateOfProjectStartInput).toHaveValue("");
    await expect(pom.projectForm.projectLocationInput).toHaveValue("");
    await expect(pom.projectForm.roleInput).toHaveValue("");
    await expect(pom.projectForm.remoteCheckbox).toBeChecked();
    await expect(pom.projectForm.remoteNumberInput).toHaveValue("100");
    await expect(pom.projectForm.utilizationCheckbox).not.toBeChecked();
    await expect(pom.projectForm.utilizationInput).toBeDisabled();
    await expect(pom.projectForm.utilizationInput).toHaveValue("100");

    await expect(pom.projectForm.createdForUser).toHaveValue("");
    await expect(pom.projectForm.platformInput).toHaveValue("");
    await expect(pom.projectForm.rateOnsiteInput).toHaveValue("");
    await expect(pom.projectForm.rateOffsiteInput).toHaveValue("");

    await expect(pom.projectForm.agencyInput).toHaveValue("");
    await expect(pom.projectForm.emailInput).toHaveValue("");
    await expect(pom.projectForm.contactFirstNameInput).toHaveValue("");
    await expect(pom.projectForm.contactLastNameInput).toHaveValue("");
    await expect(pom.projectForm.phoneInput).toHaveValue("");
    await expect(pom.projectForm.commentInput).toHaveValue("");

    await expect(pom.projectForm.projectTitleInputWarning).toBeHidden();
    await expect(pom.projectForm.dateOfApplicationInputWarning).toBeHidden();
    await expect(pom.projectForm.createdForUserInputWarning).toBeHidden();
    await expect(pom.projectForm.remoteInputWarning).toBeHidden();
    await expect(pom.projectForm.utilizationInputWarning).toBeHidden();
    await expect(pom.projectForm.percentageInputWarning).toBeHidden();
  });

  test("should validate and display a warning if Project Title is missing before saving", async () => {
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.projectTitleInputWarning).toContainText(
      "Project Title is required"
    );
    await pom.projectForm.projectTitleInput.fill("Test Project");
    await expect(pom.projectForm.projectTitleInputWarning).toBeHidden();
  });

  test("should validate and display a warning if date of application is missing before saving", async () => {
    await pom.projectForm.dateOfApplicationInput.clear();
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.dateOfApplicationInputWarning).toContainText(
      "Date of Application is required"
    );
    await pom.projectForm.dateOfApplicationInput.fill(new Date().toISOString().split("T")[0]);
    await expect(pom.projectForm.dateOfApplicationInputWarning).toBeHidden();
  });

  test("should validate and display a warning if created for user is missing before saving", async () => {
    await pom.projectForm.createdForUser.selectOption("Select a user");
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.createdForUserInputWarning).toContainText(
      "Created for is required"
    );
    await pom.projectForm.createdForUser.selectOption("John Doe");
    await expect(pom.projectForm.createdForUserInputWarning).toBeHidden();
  });

  test("should validate and display a warning for invalid email format before saving", async () => {
    await pom.projectForm.emailInput.fill("test");
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.emailInputWarning).toContainText("Enter a valid email");
    await pom.projectForm.emailInput.fill("<EMAIL>");
    await expect(pom.projectForm.emailInputWarning).toBeHidden();
  });

  test("should validate and display a warning for invalid phone format before saving", async () => {
    await pom.projectForm.phoneInput.fill("test");
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.phoneInputWarning).toContainText(
      "Enter a valid international phone number"
    );
    await pom.projectForm.phoneInput.fill("+123456789");
    await expect(pom.projectForm.phoneInputWarning).toBeHidden();
  });

  test("should validate and display a warning for invalid percentage format before saving", async () => {
    await pom.projectForm.utilizationCheckbox.check();
    await pom.projectForm.utilizationInput.fill("110");
    await pom.projectForm.remoteNumberInput.fill("110");
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.percentageInputWarning).toContainText("Between  0-100 allowed");
    await pom.projectForm.utilizationCheckbox.uncheck();
    await expect(pom.projectForm.remoteInputWarning).toContainText("Between 0-100 allowed");
    await pom.projectForm.remoteCheckbox.uncheck();
    await expect(pom.projectForm.remoteNumberInput).toBeDisabled();
    await expect(pom.projectForm.remoteNumberInput).toHaveValue("0");
    await pom.projectForm.utilizationCheckbox.check();
    await pom.projectForm.utilizationInput.fill("110");
    await expect(pom.projectForm.utilizationInputWarning).toContainText("Between 0-100 allowed");
  });

  test("should be able to add a project", async () => {
    const fakeProject = await pom.projectForm.addFakeProject();
    await pom.projectForm.saveProjectButton.click();

    const newProject = pom.projectsPage.projectItem.filter({
      hasText: fakeProject.fakeTitle,
    });
    const newProjectContent = await newProject.nth(0).textContent();

    expect(newProjectContent).toContain(fakeProject.fakeTitle);
    expect(newProjectContent).toContain(fakeProject.fakeReference);
    expect(newProjectContent).toContain(fakeProject.fakeRole);
    expect(newProjectContent).toContain(fakeProject.fakePlatform);
    expect(newProjectContent).toContain(fakeProject.fakeRate);
    expect(newProjectContent).toContain(fakeProject.fakeAgency);
    expect(newProjectContent).toContain(fakeProject.fakeContactFirstName);
    expect(newProjectContent).toContain(fakeProject.fakeContactLastName);
    expect(newProjectContent).toContain(fakeProject.fakeEmail);
    expect(newProjectContent).toContain(fakeProject.fakePhone);
    expect(newProjectContent).toContain(fakeProject.fakeComment);
    expect(newProjectContent).toContain(fakeProject.fakeLocation);
    expect(newProjectContent).toContain(fakeProject.fakeRemoteNumber);
    expect(newProjectContent).toContain(fakeProject.fakeUtilization);
    expect(newProjectContent).toContain(
      fakeProject.fakeDateOfProjectStart.split("-").reverse().join(".")
    );
    expect(newProjectContent).toContain(fakeProject.fakeUser);

    await newProject.locator(pom.projectsPage.trashIcon).click();
    await pom.projectsPage.confirmButton.click();
  });

  test("Should add createdByUser from context", async () => {
    await pom.projectsPage.openProjectForm();
    await pom.projectForm.createdForUser.selectOption("technical.project-scout");
    await pom.projectForm.projectTitleInput.fill("Test Project");

    await pom.projectForm.saveProjectButton.click();

    const testProject = pom.projectsPage.projectItem.filter({
      hasText: "Test Project",
    });

    expect(await testProject.textContent()).toContain("bytechnical.project-scoutPlatform");

    await testProject.locator(pom.projectsPage.trashIcon).click();
    await pom.projectsPage.confirmButton.click();
  });
});
