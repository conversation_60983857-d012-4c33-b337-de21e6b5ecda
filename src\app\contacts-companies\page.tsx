"use client";

import { useState } from "react";

import { NotificationToast } from "@/components/shared";

export default function ContactsCompanies() {
  const [toastMessage, setToastMessage] = useState("");

  const closeToast = () => setToastMessage("");

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Contacts & Companies</h1>
            <p className="text-lg text-gray-600 mb-8">Manage your contacts and company relationships</p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-center justify-center mb-4">
                <svg className="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                  />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-blue-900 mb-2">Coming Soon</h2>
              <p className="text-blue-700">This page is under development. Contact and company management features will be available here soon.</p>
            </div>

            <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Contact Management</h3>
                <p className="text-gray-600 text-sm">Store and organize contact information for individuals you work with on projects.</p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Company Profiles</h3>
                <p className="text-gray-600 text-sm">Maintain detailed company profiles and track business relationships.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <NotificationToast message={toastMessage} visible={!!toastMessage} onClose={closeToast} />
    </div>
  );
}
