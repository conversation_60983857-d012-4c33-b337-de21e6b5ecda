import { test, expect, type Locator } from "@playwright/test";
import { PomManager } from "@@/playwright/page-object-models/pom-manager";

test.describe("Project title", () => {
  // Run in serial mode to prevent database conflicts when updating project links
  test.describe.configure({ mode: "serial" });
  let pom: PomManager;
  let project: Locator;

  test.beforeEach(async ({ page }) => {
    pom = new PomManager(page);
    await pom.projectsPage.goToProjectsPage();
    project = pom.projectsPage.projectItem.first();
  });

  const saveProject = async () => {
    await pom.projectForm.saveProjectButton.click();
    await pom.projectsPage.projectForm.waitFor({ state: "hidden" });
    project = pom.projectsPage.projectItem.first();
  };

  const linkForms = [
    { link: "google.com", expected: "https://www.google.com/" },
    { link: "www.google.com", expected: "https://www.google.com/" },
    { link: "http://www.google.com", expected: "https://www.google.com/" },
    { link: "https://www.google.com", expected: "https://www.google.com/" },
  ];

  linkForms.forEach(({ link, expected }) => {
    test(`should open projectLink with ${link} in a new tab on projectLink click`, async ({
      page,
    }) => {
      await project.locator(pom.projectsPage.editIcon).click();
      await pom.projectForm.projectLinkInput.fill(link);
      await saveProject();

      await expect(project.locator(pom.projectsPage.projectLink)).toBeVisible();

      const [newPage] = await Promise.all([
        page.context().waitForEvent("page"),
        await project.locator(pom.projectsPage.projectTitle).click(),
      ]);

      const linkRegex = new RegExp(`${expected}.*`);
      expect(newPage.url()).toMatch(linkRegex);
    });
  });

  test("should appear plain test when no link is provided", async () => {
    await project.locator(pom.projectsPage.editIcon).click();
    await pom.projectForm.projectLinkInput.fill("");
    await saveProject();

    await expect(project.locator(pom.projectsPage.projectLink)).toBeHidden();

    const tagName = await project
      .locator(pom.projectsPage.projectTitle)
      .evaluate((el) => el.tagName);

    expect(tagName).not.toBe("A");
    await project.locator(pom.projectsPage.trashIcon).click();
    await pom.projectsPage.confirmButton.click();
  });
});
