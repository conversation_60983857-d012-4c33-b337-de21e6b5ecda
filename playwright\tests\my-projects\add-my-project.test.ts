import { test, expect } from "@playwright/test";

import { PomManager } from "@@/playwright/page-object-models/pom-manager";

test.describe("Adding a project from My Projects page", () => {
  // Run in serial mode to prevent database conflicts when creating projects
  test.describe.configure({ mode: "serial" });
  let pom: PomManager;

  test.beforeEach(async ({ page }) => {
    pom = new PomManager(page);
    await pom.myProjectsPage.goToMyProjectsPage();
    await pom.myProjectsPage.openProjectForm();
  });

  test("should be able to add a project and see it in my projects", async () => {
    const fakeProject = await pom.projectForm.addFakeProject();
    await pom.projectForm.saveProjectButton.click();

    const newProject = pom.myProjectsPage.projectItem.filter({
      hasText: fakeProject.fakeTitle,
    });
    const newProjectContent = await newProject.nth(0).textContent();

    expect(newProjectContent).toContain(fakeProject.fakeTitle);
    expect(newProjectContent).toContain("technical.project-scout");
  });
});