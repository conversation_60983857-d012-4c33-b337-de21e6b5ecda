import { test, expect } from "@playwright/test";
import { PomManager } from "@@/playwright/page-object-models/pom-manager";

test.describe("Should update input fields in a project", () => {
  // Run in serial mode to prevent database conflicts when updating projects
  test.describe.configure({ mode: "serial" });
  let pom: PomManager;
  let knownProjectData: Record<string, string>;

  test.beforeEach(async ({ page }) => {
    pom = new PomManager(page);
    await pom.projectsPage.goToProjectsPage();
    knownProjectData = await pom.projectsPage.getFirstProject();
    await pom.projectsPage.projectItem.first().locator(pom.projectsPage.editIcon).click();
    await pom.projectsPage.projectForm.waitFor({ state: "visible" });
  });

  const saveProject = async () => {
    await pom.projectForm.saveProjectButton.click();
    await pom.projectsPage.projectForm.waitFor({ state: "hidden" });
    knownProjectData = await pom.projectsPage.getFirstProject();
  };

  test("should be populated with project data", async () => {
    await expect(pom.projectForm.projectLinkInput).toHaveValue(knownProjectData.projectLink);
    await expect(pom.projectForm.projectJobReferenceInput).toHaveValue(
      knownProjectData.jobReference.replace("#", "")
    );
    await expect(pom.projectForm.statusInput).toHaveValue("active");
    await expect(pom.projectForm.projectTitleInput).toHaveValue(knownProjectData.projectTitle);
    await expect(pom.projectForm.dateOfApplicationInput).toHaveValue(
      knownProjectData.dateOfApplication.split(".").reverse().join("-")
    );
    await expect(pom.projectForm.dateOfProjectStartInput).toHaveValue(
      knownProjectData.dateOfProjectStart.split(".").reverse().join("-")
    );
    await expect(pom.projectForm.projectLocationInput).toHaveValue(knownProjectData.location);
    await expect(pom.projectForm.roleInput).toHaveValue(knownProjectData.projectRole);
    await expect(pom.projectForm.remoteNumberInput).toHaveValue(
      knownProjectData.remotePercentage.replace("%", "")
    );
    if (Number(knownProjectData.remotePercentage.replace("%", "")) > 0) {
      await expect(pom.projectForm.remoteCheckbox).toBeChecked();
      await expect(pom.projectForm.remoteNumberInput).toBeEnabled();
    } else {
      await expect(pom.projectForm.remoteCheckbox).not.toBeChecked();
      await expect(pom.projectForm.remoteNumberInput).toBeDisabled();
    }
    await expect(pom.projectForm.utilizationInput).toHaveValue(
      knownProjectData.utilization.replace("%", "")
    );
    if (Number(knownProjectData.utilization.replace("%", "")) < 100) {
      await expect(pom.projectForm.utilizationCheckbox).toBeChecked();
      await expect(pom.projectForm.utilizationCheckbox).toBeEnabled();
    } else {
      await expect(pom.projectForm.utilizationCheckbox).not.toBeChecked();
      await expect(pom.projectForm.utilizationInput).toBeDisabled();
    }

    expect(await pom.projectForm.createdForUser.locator("option:checked").textContent()).toEqual(
      knownProjectData.createdForUser
    );
    await expect(pom.projectForm.platformInput).toHaveValue(knownProjectData.platform);
    await expect(pom.projectForm.rateOnsiteInput).toHaveValue(knownProjectData.rateOnsite);
    await expect(pom.projectForm.rateOffsiteInput).toHaveValue(knownProjectData.rateOffsite);

    await expect(pom.projectForm.agencyInput).toHaveValue(knownProjectData.agency);
    await expect(pom.projectForm.emailInput).toHaveValue(knownProjectData.contactEmail);
    await expect(pom.projectForm.contactFirstNameInput).toHaveValue(
      knownProjectData.contactFirstName
    );
    await expect(pom.projectForm.contactLastNameInput).toHaveValue(
      knownProjectData.contactLastName
    );
    await expect(pom.projectForm.phoneInput).toHaveValue(knownProjectData.contactNumber);
    await expect(pom.projectForm.commentInput).toHaveValue(knownProjectData.projectComment);
  });

  test("should validate and display a warning if Project Title is missing before saving", async () => {
    await pom.projectForm.projectTitleInput.clear();
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.projectTitleInputWarning).toContainText(
      "Project Title is required"
    );
    await pom.projectForm.projectTitleInput.fill("Test Project");
    await expect(pom.projectForm.projectTitleInputWarning).toBeHidden();
  });

  test("should validate and display a warning if date of application is missing before saving", async () => {
    await pom.projectForm.dateOfApplicationInput.clear();
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.dateOfApplicationInputWarning).toContainText(
      "Date of Application is required"
    );
    await pom.projectForm.dateOfApplicationInput.fill(new Date().toISOString().split("T")[0]);
    await expect(pom.projectForm.dateOfApplicationInputWarning).toBeHidden();
  });

  test("should validate and display a warning if created for user is missing before saving", async () => {
    await pom.projectForm.createdForUser.selectOption("Select a user");
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.createdForUserInputWarning).toContainText(
      "Created for is required"
    );
    await pom.projectForm.createdForUser.selectOption("John Doe");
    await expect(pom.projectForm.createdForUserInputWarning).toBeHidden();
  });

  test("should validate and display a warning for invalid email format before saving", async () => {
    await pom.projectForm.emailInput.fill("test");
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.emailInputWarning).toContainText("Enter a valid email");
    await pom.projectForm.emailInput.fill("<EMAIL>");
    await expect(pom.projectForm.emailInputWarning).toBeHidden();
  });

  test("should validate and display a warning for invalid phone format before saving", async () => {
    await pom.projectForm.phoneInput.fill("test");
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.phoneInputWarning).toContainText(
      "Enter a valid international phone number"
    );
    await pom.projectForm.phoneInput.fill("+123456789");
    await expect(pom.projectForm.phoneInputWarning).toBeHidden();
  });

  test("should validate and display a warning for invalid percentage format before saving", async () => {
    await pom.projectForm.utilizationCheckbox.check();
    await pom.projectForm.utilizationInput.fill("110");
    await pom.projectForm.remoteCheckbox.check();
    await pom.projectForm.remoteNumberInput.fill("110");
    await pom.projectForm.saveProjectButton.click();

    await expect(pom.projectForm.percentageInputWarning).toContainText("Between 0-100 allowed");
    await pom.projectForm.utilizationCheckbox.uncheck();
    await expect(pom.projectForm.remoteInputWarning).toContainText("Between 0-100 allowed");
    await pom.projectForm.remoteCheckbox.uncheck();
    await expect(pom.projectForm.remoteCheckbox).not.toBeChecked();
    await expect(pom.projectForm.remoteNumberInput).toBeDisabled();
    await expect(pom.projectForm.remoteNumberInput).toHaveValue("0");
    await pom.projectForm.utilizationCheckbox.check();
    await pom.projectForm.utilizationInput.fill("110");
    await expect(pom.projectForm.utilizationInputWarning).toContainText("Between 0-100 allowed");
  });

  test("should update project status", async () => {
    await pom.projectForm.statusInput.selectOption("closed");
    await saveProject();
    expect(knownProjectData.projectStatus).toBe("closed");
  });

  test("should update project date of application", async () => {
    await pom.projectForm.dateOfApplicationInput.fill("2022-12-12");
    await saveProject();
    expect(knownProjectData.dateOfApplication).toBe("12.12.2022");
  });

  test("should update created for user", async () => {
    // we don't know the initial value of createdForUser (1 of 3) therefore we need to change it twice
    // to make sure it is updated and we not set the same value as the initial value
    await pom.projectForm.createdForUser.selectOption("Jane Doe");
    await saveProject();
    expect(knownProjectData.createdForUser).toBe("Jane Doe");

    await pom.projectsPage.projectItem.first().locator(pom.projectsPage.editIcon).click();
    await pom.projectForm.createdForUser.selectOption("John Doe");
    await saveProject();
    expect(knownProjectData.createdForUser).toBe("John Doe");
  });

  test("should update platform", async () => {
    await pom.projectForm.platformInput.fill("New Platform");
    await saveProject();
    expect(knownProjectData.platform).toBe("New Platform");
  });

  test("should update rateOnsite offered", async () => {
    await pom.projectForm.rateOnsiteInput.fill("100");
    await saveProject();
    expect(knownProjectData.rateOnsite).toBe("100");
  });

  test("should update project title", async () => {
    await pom.projectForm.projectTitleInput.fill("New Project Title");
    await saveProject();
    expect(knownProjectData.projectTitle).toBe("New Project Title");
  });

  test("should update project link", async () => {
    await pom.projectForm.projectLinkInput.fill("https://new-project-link.com");
    await saveProject();
    expect(knownProjectData.projectLink).toBe("https://new-project-link.com");
  });

  test("should update location", async () => {
    await pom.projectForm.projectLocationInput.fill("New Location");
    await saveProject();
    expect(knownProjectData.location).toBe("New Location");
  });

  test("should update remote", async () => {
    await pom.projectForm.remoteCheckbox.check();
    await pom.projectForm.remoteNumberInput.fill("69");
    await saveProject();
    expect(knownProjectData.remotePercentage).toBe("69%");
  });

  test("should update agency", async () => {
    await pom.projectForm.agencyInput.fill("New Agency");
    await saveProject();
    expect(knownProjectData.agency).toBe("New Agency");
  });

  test("should update contact first name", async () => {
    await pom.projectForm.contactFirstNameInput.fill("New Contact First Name");
    await saveProject();
    expect(knownProjectData.contactFirstName).toBe("New Contact First Name");
  });

  test("should update contact last name", async () => {
    await pom.projectForm.contactLastNameInput.fill("New Contact Last Name");
    await saveProject();
    expect(knownProjectData.contactLastName).toBe("New Contact Last Name");
  });

  test("should update email", async () => {
    await pom.projectForm.emailInput.fill("<EMAIL>");
    await saveProject();
    expect(knownProjectData.contactEmail).toBe("<EMAIL>");
  });

  test("should update phone number", async () => {
    await pom.projectForm.phoneInput.fill("1234567890");
    await saveProject();
    expect(knownProjectData.contactNumber).toBe("1234567890");
  });

  test("should update comment", async () => {
    await pom.projectForm.commentInput.fill("New Comment");
    await saveProject();
    expect(knownProjectData.projectComment).toBe("New Comment");
  });
});
