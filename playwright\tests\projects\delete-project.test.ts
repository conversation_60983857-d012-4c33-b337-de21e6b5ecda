import { test, expect, type Locator } from "@playwright/test";
import { PomManager } from "@@/playwright/page-object-models/pom-manager";
import { getAllFormValueByTestId } from "@@/playwright/utils/testDataUtils";

test.describe("Projects Deletion tests", () => {
  // Run in serial mode to prevent database conflicts when deleting projects
  test.describe.configure({ mode: "serial" });
  let pom: PomManager;
  let knownProject: Locator;
  let initialProjectId: string;

  test.beforeEach(async ({ page }) => {
    pom = new PomManager(page);
    await pom.projectsPage.goToProjectsPage();
    knownProject = pom.projectsPage.projectItem.first();
    initialProjectId = await pom.projectsPage.getFormValueFromProject(
      knownProject,
      pom.projectsPage.projectId
    );
  });

  test("should ask a confirmation prior deleting a project", async () => {
    await knownProject.locator(pom.projectsPage.trashIcon).click();
    await expect(pom.projectsPage.confirmDialog).toBeVisible();
    await expect(pom.projectsPage.confirmDialog).toContainText(
      "Are you sure you want to delete this project"
    );
  });

  test("should close confirmation dialog prior cancelling project deletion", async () => {
    await knownProject.locator(pom.projectsPage.trashIcon).click();
    await pom.projectsPage.cancelConfirmButton.click();
    await expect(pom.projectsPage.confirmDialog).toBeHidden();
  });

  test("should be able to delete a project upon confirmation and give a feedback", async ({
    page,
  }) => {
    await page.clock.setFixedTime(new Date());

    await knownProject.locator(pom.projectsPage.trashIcon).click();
    await pom.projectsPage.confirmButton.click();

    await expect(pom.projectsPage.toastNotification).toBeVisible();
    await expect(pom.projectsPage.toastNotification).toContainText("Project deleted successfully");

    const allProjectIdsOnPage = await getAllFormValueByTestId(pom, "projectId");
    expect(allProjectIdsOnPage).not.toContain(initialProjectId);

    await page.clock.runFor(4000);
    await expect(pom.projectsPage.toastNotification).toBeHidden();
  });
});
