import { test, expect } from "@playwright/test";

import { PomManager } from "@@/playwright/page-object-models/pom-manager";
import { TestDataUtils } from "../../utils/testDataUtils";

test.describe("Search My Projects tests", () => {
  // Run in serial mode to prevent database conflicts when creating test data
  test.describe.configure({ mode: "serial" });
  let pom: PomManager;
  const testDataUtils = new TestDataUtils();
  let testProject: Record<string, string>;

  test.beforeAll(async () => {
    testProject = await testDataUtils.addUniqueProject();
  });

  test.beforeEach(async ({ page }) => {
    pom = new PomManager(page);
    await pom.myProjectsPage.goToMyProjectsPage();
    await pom.myProjectsPage.searchInput.fill("");
  });

  test.afterAll(async () => {
    await testDataUtils.deleteUniqueProjects(testProject.projectTitle);
    await testDataUtils.disconnect();
  });

  test("should only search within current user's projects", async () => {
    await pom.myProjectsPage.searchInput.fill(testProject.projectTitle);
    const searchResults = await pom.myProjectsPage.projectCount();

    expect(searchResults).toBe(1);

    // Verify the found project belongs to current user
    const foundProject = pom.myProjectsPage.projectItem.first();
    const createdByUser = await pom.myProjectsPage.getFormValueFromProject(
      foundProject,
      pom.myProjectsPage.createdForUser
    );
    expect(createdByUser).toContain("technical.project-scout");
  });
});